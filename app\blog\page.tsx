import Header from '@/components/Header';
import Footer from '@/components/Footer';
import SocialLinks from '@/components/SocialLinks';
import ScrollIndicator from '@/components/ScrollIndicator';
import MediumArticles from '@/components/MediumArticles';

export default function BlogPage() {
  return (
    <>
      <Header />
      <SocialLinks />
      <ScrollIndicator />

      <main className="pt-20 md:pt-24">
        <section className="py-20 bg-gray-100 relative overflow-hidden">
          {/* Large "BLOG" text in background */}
          <div className="absolute inset-0 pointer-events-none select-none overflow-hidden">
            <h2 className="text-[160px] md:text-[250px] lg:text-[300px] font-bold text-gray-200 opacity-50 tracking-widest absolute left-1/2 top-0 transform -translate-x-1/2">
              BLOG
            </h2>
          </div>

          <div className="container mx-auto px-6 md:px-12 relative z-10">
            <div className="max-w-4xl mx-auto text-center mb-16">
              <h2 className="text-blue-600 text-xl mb-4">MY THOUGHTS</h2>
              <h3 className="text-3xl md:text-4xl font-bold mb-6 text-gray-800">
                Latest Articles
              </h3>
              <p className="text-lg text-gray-600">
                I write about web development, design, and technology. Check out my latest articles below.
              </p>
            </div>

            <div className="max-w-4xl mx-auto">
              <MediumArticles />
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
} 