'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';

interface MediumArticle {
  title: string;
  link: string;
  pubDate: string;
  thumbnail: string;
  description: string;
}

export default function BlogSection() {
  const [articles, setArticles] = useState<MediumArticle[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchMediumArticles = async () => {
      try {
        const response = await fetch('/api/medium-articles');
        const data = await response.json();
        setArticles(data.slice(0, 3)); // Show only the 3 most recent articles
      } catch (error) {
        console.error('Error fetching Medium articles:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMediumArticles();
  }, []);

  return (
    <section id="blog" className="py-20 bg-gray-100 relative overflow-hidden">
      {/* Large "BLOG" text in background */}
      <div className="absolute inset-0 pointer-events-none select-none overflow-hidden">
        <h2 className="text-[160px] md:text-[250px] lg:text-[300px] font-bold text-gray-200 opacity-50 tracking-widest absolute left-1/2 top-0 transform -translate-x-1/2">
          BLOG
        </h2>
      </div>
      
      <div className="container mx-auto px-6 md:px-12 relative z-10">
        <div className="max-w-4xl mx-auto text-center mb-16">
          <h2 className="text-blue-600 text-xl mb-4">MY THOUGHTS</h2>
          <h3 className="text-3xl md:text-4xl font-bold mb-6 text-gray-800">
            Latest Articles
          </h3>
          <p className="text-lg text-gray-600">
In my blog, I share my personal coding journey, thoughts on technology, and topics that interest me as I grow as a developer. I write about what I'm learning, the challenges I face, and insights I gain as I continue to build my skills in fullstack development, mobile apps, and data science.          </p>
        </div>
        
        {loading ? (
          <div className="flex justify-center items-center min-h-[200px]">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {articles.map((article, index) => (
              <div key={index} className="bg-white rounded-lg overflow-hidden shadow-lg transition-transform hover:scale-105">
                <Link href={article.link} target="_blank" rel="noopener noreferrer" className="block">
                  <div className="relative w-full h-48">
                    <Image
                      src={article.thumbnail}
                      alt={article.title}
                      fill
                      className="object-cover rounded-t-lg"
                      sizes="(max-width: 768px) 100vw, 33vw"
                    />
                  </div>
                  <div className="p-6">
                    <div className="flex items-center text-sm text-gray-500 mb-2">
                      <span>{new Date(article.pubDate).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}</span>
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-gray-800">{article.title}</h3>
                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {article.description.replace(/<[^>]*>/g, '')}
                    </p>
                    <span className="text-blue-600 font-medium inline-flex items-center">
                      Read on Medium
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                      </svg>
                    </span>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        )}
        
        {/* View All Blog Posts Button */}
        <div className="text-center mt-12">
          <Link 
            href="/blog" 
            className="px-8 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors inline-flex items-center"
          >
            View All Articles
            <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
}
