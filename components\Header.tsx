'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const headerRef = useRef<HTMLElement>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  // Handle scroll events to hide/show header
  useEffect(() => {
    const controlHeader = () => {
      const currentScrollY = window.scrollY;
      
      // Only show header when at the very top of the page
      if (currentScrollY <= 10) {
        setIsVisible(true);
        setIsScrolling(false);
      } else {
        // Hide header when scrolling in any direction
        setIsVisible(false);
        setIsScrolling(true);
        
        // Close menu when header is hidden
        if (isMenuOpen) setIsMenuOpen(false);
        
        // Clear any existing timeout
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
        
        // Set a timeout to show the header again after scrolling stops
        scrollTimeoutRef.current = setTimeout(() => {
          setIsScrolling(false);
        }, 1000);
      }
      
      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', controlHeader);
    return () => {
      window.removeEventListener('scroll', controlHeader);
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, [lastScrollY, isMenuOpen]);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        menuRef.current &&
        buttonRef.current &&
        !menuRef.current.contains(event.target as Node) &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <header
      ref={headerRef}
      className={`fixed left-0 right-0 z-50 py-4 transition-transform duration-300 ${
        isVisible ? 'top-0 translate-y-0' : '-translate-y-full'
      }`}
    >
      <div className="container mx-auto px-6 md:px-12 flex justify-between items-center">
        <Link href="/" className="text-blue-600 font-bold text-xl">
          <span className="text-blue-600">devndata</span>
        </Link>

        {/* Hamburger menu button */}
        <button
          ref={buttonRef}
          onClick={toggleMenu}
          className="focus:outline-none"
          aria-label="Toggle menu"
        >
          <svg
            className="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            {isMenuOpen ? (
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            ) : (
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16M4 18h16"
              />
            )}
          </svg>
        </button>

        {/* Navigation Menu (Popup) */}
        {isMenuOpen && (
          <div
            ref={menuRef}
            className="fixed top-20 right-6 bg-white shadow-lg rounded-lg p-6 w-56 animate-fadeIn"
            style={{
              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
              transform: 'translateY(0)',
              opacity: 1,
              transition: 'all 0.2s ease-in-out',
              zIndex: 100
            }}
          >
            <nav className="flex flex-col space-y-5">
            <Link
                href="/"
                className="nav-link text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium"
                onClick={toggleMenu}
              >
                Home
              </Link>
              <Link
                href="/about"
                className="nav-link text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium"
                onClick={toggleMenu}
              >
                About
              </Link>
              
              <Link
                href="/projects"
                className="nav-link text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium"
                onClick={toggleMenu}
              >
                Projects
              </Link>
              <Link
                href="/blog"
                className="nav-link text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium"
                onClick={toggleMenu}
              >
                Blogs
              </Link>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}

