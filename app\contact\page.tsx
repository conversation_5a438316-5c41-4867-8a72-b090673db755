import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ContactForm from '@/components/ContactForm';
import Link from 'next/link';

export default function ContactPage() {
  return (
    <>
      <Header />
      
      <main className="pt-24 pb-16 bg-white">
        <div className="container mx-auto px-6 max-w-4xl">
          <h1 className="text-3xl font-bold mb-12 text-center">Contact</h1>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            {/* Contact Information */}
            <div>
              <h2 className="text-xl font-medium mb-6">Contact Information</h2>
              
              <ul className="space-y-4">
                <li className="flex items-center">
                  <span className="w-24 text-gray-600">Email:</span>
                  <a href="mailto:<EMAIL>" className="text-blue-600"><EMAIL></a>
                </li>
                <li className="flex items-center">
                  <span className="w-24 text-gray-600">Phone:</span>
                  <a href="tel:+252619758675" className="text-blue-600">+252 619 758 675</a>
                </li>
                <li className="flex items-center">
                  <span className="w-24 text-gray-600">LinkedIn:</span>
                  <a href="https://www.linkedin.com/in/hasina-goosaar-357756268" target="_blank" rel="noopener noreferrer" className="text-blue-600">Hasina Goosaar</a>
                </li>
                <li className="flex items-center">
                  <span className="w-24 text-gray-600">GitHub:</span>
                  <a href="https://github.com/hasina-dev" target="_blank" rel="noopener noreferrer" className="text-blue-600">hasina-dev</a>
                </li>
              </ul>
            </div>
            
            {/* Contact Form */}
            <div>
              <h2 className="text-xl font-medium mb-6">Send a Message</h2>
              <ContactForm />
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </>
  );
}


