import Image from 'next/image';

const skills = [
  { name: 'HTML5', icon: '/icons/html5.svg' },
  { name: 'CSS3', icon: '/icons/css3.svg' },
  { name: 'JavaScript', icon: '/icons/javascript.svg' },
  { name: 'TypeScript', icon: '/icons/typescript.svg' },
  { name: 'SASS', icon: '/icons/sass.svg' },
  { name: 'Vue', icon: '/icons/vue.svg' },
  { name: 'React', icon: '/icons/react.svg' },
  { name: 'Angular', icon: '/icons/angular.svg' },
  { name: 'Firebase', icon: '/icons/firebase.svg' },
  { name: 'Figma', icon: '/icons/figma.svg' },
  { name: 'Frima', icon: '/icons/frima.svg' },
  { name: 'WordPress', icon: '/icons/wordpress.svg' },
  { name: 'GitHub', icon: '/icons/github.svg' },
  { name: 'Git', icon: '/icons/git.svg' },
  { name: 'Tailwind', icon: '/icons/tailwind.svg' },
];

export default function SkillsSection() {
  return (
    <section id="skills" className="py-20 bg-gray-100 text-gray-800 relative overflow-hidden">
      {/* Large "SKILLS" text in background */}
      <div className="absolute inset-0 pointer-events-none select-none overflow-hidden">
        <h2 className="text-[160px] md:text-[250px] lg:text-[300px] font-bold text-gray-200 opacity-50 tracking-widest absolute left-1/2 top-0 transform -translate-x-1/2">
          SKILLS
        </h2>
      </div>

      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <div className="max-w-4xl mx-auto md:ml-20">
          <h2 className="text-blue-600 text-xl mb-4">WHICH <span className="text-gray-800">TOOLS DO I USE?</span></h2>
          <p className="text-lg mb-12 max-w-2xl">
            The skills, tools, and technologies I've worked with:
          </p>

          <div className="grid grid-cols-3 md:grid-cols-5 gap-x-8 gap-y-12 max-w-4xl mx-auto">
            {skills.map((skill, index) => (
              <div key={index} className="flex flex-col items-center">
                <Image
                  src={skill.icon}
                  alt={skill.name}
                  width={40}
                  height={40}
                  className="w-10 h-10 mb-3"
                />
                <span className="text-sm font-light">{skill.name}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
