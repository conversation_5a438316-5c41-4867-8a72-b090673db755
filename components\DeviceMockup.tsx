import React from 'react';
import Image from 'next/image';

type DeviceType = 'browser' | 'mobile' | 'tablet';

interface DeviceMockupProps {
  type: DeviceType;
  screenshot: string;
  alt: string;
  className?: string;
}

export default function DeviceMockup({ type, screenshot, alt, className = '' }: DeviceMockupProps) {
  if (type === 'browser') {
    return (
      <div className={`relative ${className}`}>
        {/* Browser mockup - styled to match the examples */}
        <div className="bg-white rounded-lg overflow-hidden shadow-2xl border border-gray-200">
          {/* Browser header */}
          <div className="bg-gray-100 px-4 py-2 flex items-center border-b border-gray-200">
            <div className="flex space-x-2 mr-4">
              <div className="w-3 h-3 rounded-full bg-red-400"></div>
              <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
              <div className="w-3 h-3 rounded-full bg-green-400"></div>
            </div>
            <div className="flex-1 bg-white rounded-full h-6 flex items-center justify-center text-xs text-gray-500 px-3">
              example.com
            </div>
          </div>
          
          {/* Browser content */}
          <div className="relative aspect-[16/9]">
            <Image
              src={screenshot}
              alt={alt}
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    );
  }
  
  if (type === 'mobile') {
    return (
      <div className={`relative ${className}`}>
        {/* Mobile device mockup */}
        <div className="relative mx-auto w-64 h-auto">
          {/* Phone frame */}
          <div className="relative border-8 border-gray-800 rounded-[2.5rem] overflow-hidden shadow-xl">
            {/* Notch */}
            <div className="absolute top-0 inset-x-0 h-6 bg-gray-800 rounded-b-lg z-10"></div>
            
            {/* Screen */}
            <div className="relative aspect-[9/19.5]">
              <Image
                src={screenshot}
                alt={alt}
                fill
                className="object-cover"
              />
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  // Tablet mockup
  return (
    <div className={`relative ${className}`}>
      {/* Tablet device mockup */}
      <div className="relative mx-auto w-80 h-auto">
        {/* Tablet frame */}
        <div className="relative border-12 border-gray-800 rounded-2xl overflow-hidden shadow-xl">
          {/* Screen */}
          <div className="relative aspect-[4/3]">
            <Image
              src={screenshot}
              alt={alt}
              fill
              className="object-cover"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
