import { NextResponse } from 'next/server';
import Parser from 'rss-parser';

const parser = new Parser();

interface MediumFeedItem {
  title?: string;
  link?: string;
  pubDate?: string;
  contentSnippet?: string;
  content?: string;
  enclosure?: {
    url: string;
  };
}

const extractImage = (html?: string): string => {
  if (!html) return '';
  const match = html.match(/<img[^>]+src=["']([^"'>]+)["']/i);
  return match ? match[1] : '';
};

const isValidImageUrl = (url: string) => url && url.startsWith('http');

export async function GET() {
  try {
    const feed = await parser.parseURL('https://medium.com/feed/@hasinagoosaar');
    if (feed.items.length > 0) {
      const first = feed.items[0] as any;
      console.log('DEBUG: content:', first.content);
      console.log('DEBUG: content:encoded:', first['content:encoded']);
    }
    
    const articles = feed.items.map((item: MediumFeedItem & { [key: string]: any }) => {
      const htmlContent = item.content || item['content:encoded'] || '';
      const extracted = item.enclosure?.url || extractImage(htmlContent) || '';
      const thumbnail = isValidImageUrl(extracted) ? extracted : '/blog-placeholder.jpg';
      console.log('Extracted thumbnail:', thumbnail); // DEBUG LOG
      return {
        title: item.title || '',
        link: item.link || '',
        pubDate: item.pubDate || '',
        description: item.contentSnippet || item.content || '',
        thumbnail,
        contentEncoded: item['content:encoded'] || '',
      };
    });

    return NextResponse.json(articles);
  } catch (error) {
    console.error('Error fetching Medium articles:', error);
    return NextResponse.json({ error: 'Failed to fetch Medium articles' }, { status: 500 });
  }
} 