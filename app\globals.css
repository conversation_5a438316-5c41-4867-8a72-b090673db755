@import "tailwindcss";

:root {
  --background: #f5f5f7;
  --foreground: #171717;
  --primary: #3b82f6;
  --secondary: #1e293b;
  --accent: #4f46e5;
  --dark-bg: #1e293b;
  --light-text: #f8fafc;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-heading: var(--font-poppins);
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .section-title {
    @apply text-2xl md:text-3xl font-bold text-blue-600 mb-4;
  }

  .section-heading {
    @apply text-3xl md:text-4xl lg:text-5xl font-bold mb-6;
  }

  .vertical-text {
    writing-mode: vertical-rl;
    text-orientation: mixed;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
}

.social-icon {
  @apply w-6 h-6 text-gray-600 hover:text-blue-600 transition-colors;
}

.nav-link {
  @apply hover:text-blue-600 transition-colors;
}

.project-card {
  @apply bg-white rounded-lg shadow-md overflow-hidden transition-transform hover:scale-105;
}
