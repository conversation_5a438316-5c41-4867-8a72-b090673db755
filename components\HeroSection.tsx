import Link from 'next/link';

export default function HeroSection() {
  return (
    <section id="home" className="min-h-screen flex items-center pt-24 pb-12 relative overflow-hidden">
      {/* Large "HELLO" text in background */}
      <div className="absolute inset-0 pointer-events-none select-none overflow-hidden">
        <h2 className="text-[160px] md:text-[250px] lg:text-[300px] font-bold text-gray-200 opacity-30 tracking-widest absolute -right-10 top-1/2 transform -translate-y-1/2">
          HELLO
        </h2>
      </div>

      <div className="container mx-auto px-6 md:px-12 relative z-10">
        <div className="max-w-3xl mx-auto md:ml-20">
          <p className="text-gray-600 mb-2">Hello...</p>
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 font-heading">
            I&apos;m <PERSON><PERSON>, a Fullstack Developer.
          </h1>
          <p className="text-gray-600 text-lg mb-4 max-w-2xl">
I build scalable web and mobile applications, and I'm currently expanding my skills into data science. As a Fullstack Developer, I work across frontend, backend, and mobile platforms to deliver efficient, user-friendly solutions, while continuously learning new technologies to grow as a developer.</p>
          <Link
            href="/Hasina_CV.pdf"
            className="inline-flex items-center text-gray-600 hover:text-blue-600 font-medium transition-colors ml-0"
            target="_blank"
          >
            View Resume
            <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
}
