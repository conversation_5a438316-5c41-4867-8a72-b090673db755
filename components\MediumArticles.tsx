'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';

interface MediumArticle {
  title: string;
  link: string;
  pubDate: string;
  thumbnail: string;
  description: string;
  contentEncoded?: string;
}

export default function MediumArticles() {
  const [articles, setArticles] = useState<MediumArticle[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchMediumArticles = async () => {
      try {
        const response = await fetch('/api/medium-articles');
        const data = await response.json();
        if (!Array.isArray(data)) {
          setArticles([]);
        } else {
          setArticles(data);
        }
      } catch (error) {
        console.error('Error fetching Medium articles:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMediumArticles();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (articles.length === 0) {
    return (
      <div className="text-center text-gray-600 py-8">
        No articles found. Please check your Medium username configuration.
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {articles.map((article, index) => (
        <article key={index} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow flex flex-col md:flex-row md:items-stretch">
          {/* Text content */}
          <div className="flex-1 p-6 flex flex-col justify-between">
            <div>
              <div className="flex items-center text-sm text-gray-500 mb-2">
                <span>{new Date(article.pubDate).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}</span>
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800 hover:text-blue-600 transition-colors">
                {article.title}
              </h3>
              {/* Article excerpt: first real paragraph (skip title), clamped to 4 lines */}
              <p className="text-gray-600 mb-4 line-clamp-4">
                {(() => {
                  const html = article.contentEncoded || article.description || '';
                  const matches = [...html.matchAll(/<p[^>]*>(.*?)<\/p>/gi)];
                  if (matches.length > 1) {
                    return matches[1][1].replace(/&nbsp;/g, ' ').trim().slice(0, 260);
                  }
                  if (matches.length === 1) {
                    return matches[0][1].replace(/&nbsp;/g, ' ').trim().slice(0, 260);
                  }
                  const clean = html.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim();
                  return clean.slice(0, 260);
                })()}
              </p>
            </div>
            <div>
              <Link href={article.link} target="_blank" rel="noopener noreferrer" className="text-blue-600 font-medium inline-flex items-center">
                Read more on Medium
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </Link>
            </div>
          </div>
          {/* Image on right */}
          <div className="relative w-full md:w-56 h-48 md:h-auto flex-shrink-0">
            <Image
              src={article.thumbnail}
              alt={article.title}
              fill
              className="object-cover h-full w-full md:rounded-l-none md:rounded-r-lg rounded-t-lg md:rounded-t-none"
              sizes="(max-width: 768px) 100vw, 224px"
            />
          </div>
        </article>
      ))}
    </div>
  );
} 