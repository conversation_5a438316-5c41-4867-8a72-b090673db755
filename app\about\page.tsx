import Image from 'next/image';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import SocialLinks from '@/components/SocialLinks';
import ScrollIndicator from '@/components/ScrollIndicator';
import ServicesSection from '@/components/Service';

export default function AboutPage() {
  return (
    <>
      <Header />
      <SocialLinks />
      <ScrollIndicator />

      <main className="pt-20 md:pt-24">
        {/* Simplified Hero Section with blue and navy blue colors */}
        <section className="py-20 md:py-28  overflow-hidden relative">
          <div className="container mx-auto px-6 md:px-12 relative z-10">
            <div className="flex flex-col md:flex-row items-center gap-16 md:gap-20">
              <div className="md:w-1/2">
                <span className="inline-block px-4 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium mb-6">
                  Fullstack Developer
                </span>
                <h1 className="text-5xl md:text-6xl font-bold mb-8 text-gray-900 leading-tight">
                  About <span className="text-blue-600">Me</span>
                </h1>
                
                {/* Simplified text without box */}
                <div className="space-y-6">
                  <p className="text-lg text-gray-700 leading-relaxed">
                    Hello! I'm <span className="font-semibold text-blue-700">Hasina Goosaar</span>, a passionate Fullstack Developer with experience in building web and mobile applications using modern technologies. With a background in science and a strong problem-solving mindset, I bring a unique combination of analytical thinking and creativity into my development work.
                  </p>
                  <p className="text-lg text-gray-700 leading-relaxed">
                   Since starting my coding journey in 2022, I’ve been developing fullstack projects using the MERN stack (MongoDB, Express, React.js, Node.js), as well as building mobile applications and exploring cloud technologies. Currently, I’m also expanding my skills into data science, focusing on data analysis, machine learning, and Python-based tools.
                  </p>
                  <p className="text-lg text-gray-700 leading-relaxed">
                   I enjoy working in collaborative teams where I can contribute my technical skills, continue learning, and build meaningful solutions that make a real-world impact.
                  </p>
                </div>
              </div>
              
              <div className="md:w-1/2">
                <div className="relative">
                  {/* Simplified frame with blue colors */}
                  <div className="absolute -inset-4 bg-gray-100 rounded-lg rotate-3 opacity-80"></div>
                  <div className="absolute -inset-4 bg-gray-900 rounded-lg -rotate-2 opacity-80"></div>
                  <div className="absolute -inset-1 bg-gray-50 rounded-lg rotate-1 opacity-20"></div>
                  
                  {/* Image with simplified styling */}
                  <div className="relative overflow-hidden rounded-lg shadow-lg border-4 border-white">
                    <Image 
                      src="/bbg.jpeg" 
                      alt="Hasina Goosaar" 
                      width={500} 
                      height={600}
                      className="relative object-cover w-full h-auto max-h-[550px]"
                      style={{
                        objectFit: 'cover',
                        objectPosition: 'center top'
                      }}
                      priority
                    />
                  </div>
                  
                  {/* Simplified decorative element */}
                  <div className="absolute -bottom-6 -right-6 w-24 h-24 bg-blue-100 rounded-full border-4 border-white shadow-lg flex items-center justify-center">
                    <span className="text-blue-700 font-bold text-lg">5+</span>
                    <span className="text-xs text-blue-700 ml-1">Years</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Services Section */}
        <ServicesSection />
        
        {/* Education & Experience Section - Updated with navy blue */}
        <section className="py-16 md:py-20 bg-gray-900 text-white">
          <div className="container mx-auto px-6 md:px-12">
            <div className="max-w-3xl mx-auto">
              <h2 className="text-3xl md:text-4xl font-bold mb-10 text-left text-gray-200">
                Education <span className="text-blue-500">&</span> Experience
              </h2>
              <p className="text-gray-300 text-lg mb-12 max-w-2xl">
                I design and build fast, intuitive web and mobile applications that solve real problems. With a passion for clean code and seamless UX, I'm always learning and pushing my skills forward.
              </p>

              {/* Timeline for both Education and Experience */}
              <div className="relative border-l-2 border-gray-500 space-y-12 pl-8">
                {/* Experience Item 1 (Frontend Developer) */}
                <div className="relative group">
                  <div className="absolute -left-[18px] top-1 w-5 h-5 rounded-full border-2 border-blue-500 bg-navy-900 ring-4 ring-navy-900 transition-transform duration-300 group-hover:scale-110"></div>
                  <span className="inline-block border border-blue-500 text-blue-500 font-medium text-sm px-3 py-1 rounded-full mb-4">2023 - Present</span>
                  <div className="text-gray-200">
                    <h3 className="text-xl font-semibold text-white mb-1">Fullstack Developer </h3>
                    <p className="text-gray-400 mb-4 text-sm">Recent IT.</p>
                    <ul className="list-disc list-outside ml-5 text-gray-300 space-y-2 text-base leading-relaxed">
                      <li>Developed responsive web applications using React and Next.js</li>
                      <li>Collaborated with designers to implement pixel-perfect UI components</li>
                      <li>Optimized application performance and improved load times by 40%</li>
                      <li>Participated in code reviews and mentored junior developers</li>
                    </ul>
                  </div>
                </div>

                {/* Experience Item 2 (Web Development Intern) */}
                <div className="relative group">
                  <div className="absolute -left-[18px] top-1 w-5 h-5 rounded-full border-2 border-blue-500 bg-navy-900 ring-4 ring-navy-900 transition-transform duration-300 group-hover:scale-110"></div>
                  <span className="inline-block border border-blue-500 text-blue-500 font-medium text-sm px-3 py-1 rounded-full mb-4">2022 - 2023</span>
                  <div className="text-gray-200">
                    <h3 className="text-xl font-semibold text-white mb-1">Web Development Intern</h3>
                    <p className="text-gray-400 mb-4 text-sm">Digital Creatives Agency</p>
                    <ul className="list-disc list-outside ml-5 text-gray-300 space-y-2 text-base leading-relaxed">
                      <li>Assisted in building client websites using HTML, CSS, and JavaScript</li>
                      <li>Implemented responsive designs and ensured cross-browser compatibility</li>
                      <li>Created and maintained documentation for web projects</li>
                      <li>Participated in client meetings and gathered requirements</li>
                    </ul>
                  </div>
                </div>

                {/* Education Item 1 (Bachelor of Science in Computer Science) */}
                <div className="relative group">
                  <div className="absolute -left-[18px] top-1 w-5 h-5 rounded-full border-2 border-blue-500 bg-navy-900 ring-4 ring-navy-900 transition-transform duration-300 group-hover:scale-110"></div>
                  <span className="inline-block border border-blue-500 text-blue-500 font-medium text-sm px-3 py-1 rounded-full mb-4">2019 - 2023</span>
                  <div className="text-gray-200">
                    <h3 className="text-xl font-semibold text-white mb-1">Bachelor of Science in Computer Science</h3>
                    <p className="text-gray-400 mb-2 text-sm">University of Technology</p>
                    <p className="text-gray-300 text-base leading-relaxed">
                      Focused on web development, algorithms, and data structures. Graduated with honors.
                    </p>
                  </div>
                </div>

                {/* Education Item 2 (Full Stack Web Development Bootcamp) */}
                <div className="relative group">
                  <div className="absolute -left-[18px] top-1 w-5 h-5 rounded-full border-2 border-blue-500 bg-navy-900 ring-4 ring-navy-900 transition-transform duration-300 group-hover:scale-110"></div>
                  <span className="inline-block border border-blue-500 text-blue-500 font-medium text-sm px-3 py-1 rounded-full mb-4">2022</span>
                  <div className="text-gray-200">
                    <h3 className="text-xl font-semibold text-white mb-1">Full Stack Web Development Bootcamp</h3>
                    <p className="text-gray-400 mb-2 text-sm">Tech Academy</p>
                    <p className="text-gray-300 text-base leading-relaxed">
                      Intensive 12-week program covering modern web technologies including React, Node.js, and database design.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Hackathons Section - Redesigned with horizontal cards */}
        <section className="py-16 md:py-20 bg-slate-50">
          <div className="container mx-auto px-6 md:px-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-16 text-center">
              <span className="text-blue-600">Hackathons</span> & Events
            </h2>
            
            <div className="space-y-12 max-w-5xl mx-auto">
              {/* Hackathon Item 1 - Horizontal layout */}
              <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 flex flex-col md:flex-row">
                <div className="md:w-2/5 relative">
                  <Image
                    src="/1.jpg"
                    alt="TechCrunch Disrupt Hackathon"
                    width={400}
                    height={300}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-4 left-4 bg-blue-600 text-white text-sm font-medium px-3 py-1 rounded-full">
                    1st Place
                  </div>
                </div>
                <div className="p-6 md:w-3/5 flex flex-col justify-between">
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-medium text-blue-600 bg-blue-50 px-3 py-1 rounded-full">June 2023</span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-800 mb-3">TechCrunch Disrupt Hackathon</h3>
                    <p className="text-gray-600 mb-4">
                      Developed an AI-powered accessibility tool that converts website content to be more accessible for users with various disabilities. The solution automatically adapts content based on user preferences and needs, including text-to-speech, contrast adjustments, and simplified layouts.
                    </p>
                    <p className="text-gray-600 mb-4">
                      Our team of four worked for 48 hours straight to build a working prototype that impressed judges with its innovative approach to web accessibility. The tool now helps hundreds of users navigate the web more effectively.
                    </p>
                  </div>
                  <div className="flex flex-wrap gap-2 mt-auto">
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">React</span>
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">AI</span>
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">Accessibility</span>
                  </div>
                </div>
              </div>
              
              {/* Hackathon Item 2 - Horizontal layout */}
              <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 flex flex-col md:flex-row">
                <div className="md:w-2/5 relative">
                  <Image
                    src="/2.jpg"
                    alt="Global Climate Hackathon"
                    width={400}
                    height={300}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-4 left-4 bg-blue-500 text-white text-sm font-medium px-3 py-1 rounded-full">
                    2nd Place
                  </div>
                </div>
                <div className="p-6 md:w-3/5 flex flex-col justify-between">
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-medium text-blue-600 bg-blue-50 px-3 py-1 rounded-full">October 2022</span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-800 mb-3">Global Climate Hackathon</h3>
                    <p className="text-gray-600 mb-4">
                      Created a platform that helps users track and reduce their carbon footprint through personalized recommendations and community challenges. The application analyzes daily habits and suggests sustainable alternatives tailored to each user's lifestyle.
                    </p>
                    <p className="text-gray-600 mb-4">
                      The platform includes a social component where users can join community challenges and see their collective impact. Since launch, our solution has helped reduce carbon emissions by an estimated 15 tons among early adopters.
                    </p>
                  </div>
                  <div className="flex flex-wrap gap-2 mt-auto">
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">Vue.js</span>
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">Firebase</span>
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">Sustainability</span>
                  </div>
                </div>
              </div>
              
              {/* Hackathon Item 3 - Horizontal layout */}
              <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 flex flex-col md:flex-row">
                <div className="md:w-2/5 relative">
                  <Image
                    src="/2.jpg"
                    alt="HealthTech Innovation Challenge"
                    width={400}
                    height={300}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-4 left-4 bg-blue-400 text-white text-sm font-medium px-3 py-1 rounded-full">
                    Finalist
                  </div>
                </div>
                <div className="p-6 md:w-3/5 flex flex-col justify-between">
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-medium text-blue-600 bg-blue-50 px-3 py-1 rounded-full">March 2022</span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-800 mb-3">HealthTech Innovation Challenge</h3>
                    <p className="text-gray-600 mb-4">
                      Built a mobile application that connects patients with mental health resources and provides personalized wellness tracking. The app features mood tracking, guided meditation sessions, and a resource directory of mental health professionals.
                    </p>
                    <p className="text-gray-600 mb-4">
                      What set our solution apart was the integration of a machine learning algorithm that adapts content based on user patterns and reported moods. The app received recognition for its user-centered design and thoughtful approach to mental health support.
                    </p>
                  </div>
                  <div className="flex flex-wrap gap-2 mt-auto">
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">React Native</span>
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">Node.js</span>
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">Healthcare</span>
                  </div>
                </div>
              </div>
              
              {/* Hackathon Item 4 - Horizontal layout */}
              <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 flex flex-col md:flex-row">
                <div className="md:w-2/5 relative">
                  <Image
                    src="/1.jpg"
                    alt="EdTech Startup Weekend"
                    width={400}
                    height={300}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-4 left-4 bg-blue-300 text-white text-sm font-medium px-3 py-1 rounded-full">
                    Participant
                  </div>
                </div>
                <div className="p-6 md:w-3/5 flex flex-col justify-between">
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-medium text-blue-600 bg-blue-50 px-3 py-1 rounded-full">January 2022</span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-800 mb-3">EdTech Startup Weekend</h3>
                    <p className="text-gray-600 mb-4">
                      Developed a collaborative learning platform that helps students connect with peers for project-based learning and skill sharing. The platform matches students based on complementary skills and learning goals to facilitate effective collaboration.
                    </p>
                    <p className="text-gray-600 mb-4">
                      Though we didn't win, this hackathon was a valuable learning experience that taught me about rapid prototyping and the importance of user research. The connections made during this event led to future collaborations and opportunities in the EdTech space.
                    </p>
                  </div>
                  <div className="flex flex-wrap gap-2 mt-auto">
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">JavaScript</span>
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">MongoDB</span>
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">Education</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        {/* CTA Section - Redesigned with gradient and modern style */}
        <section className="py-16 md:py-24 bg-gradient-to-r from-blue-700 via-blue-600 to-indigo-700 relative overflow-hidden">
          {/* Abstract shapes for visual interest */}
          <div className="absolute top-0 left-0 w-full h-full opacity-10">
            <div className="absolute top-10 left-10 w-40 h-40 rounded-full bg-white"></div>
            <div className="absolute bottom-10 right-10 w-60 h-60 rounded-full bg-white"></div>
            <div className="absolute top-1/2 left-1/3 w-20 h-20 rounded-full bg-white"></div>
          </div>
          
          <div className="container mx-auto px-6 md:px-12 text-center relative z-10">
            <h2 className="text-3xl md:text-5xl font-bold mb-6 text-white leading-tight">
              Ready to work <span className="text-gray-900">together?</span>
            </h2>
            <p className="text-lg md:text-xl mb-10 max-w-2xl mx-auto leading-relaxed text-blue-100">
              I'm currently available for freelance projects and full-time opportunities.
              Let's create something amazing together!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/#contact" 
                className="px-8 py-4 bg-white text-blue-700 font-semibold rounded-lg hover:bg-blue-50 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                Let's Talk
              </Link>
              <a 
                href="<EMAIL>" 
                className="px-8 py-4 bg-transparent border-2 border-white text-white font-semibold rounded-lg hover:bg-white/10 transition-all duration-300"
              >
                Email Me
              </a>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </>
  );
}






