import Link from 'next/link';

export default function AboutSection() {
  return (
    <section id="about" className="py-10 text-gray-600 relative overflow-hidden ">
      {/* Large "ABOUT" text in background */}
      <div className="absolute inset-0 pointer-events-none select-none overflow-hidden">
        <h2 className="text-[160px] md:text-[250px] lg:text-[300px] font-bold text-gray-200 opacity-50 tracking-widest absolute left-1/2 top-0 transform -translate-x-1/2">
          ABOUT
        </h2>
      </div>

      <div className="container mx-auto px-6 md:px-12 relative z-10">
        <div className="max-w-3xl mx-auto md:ml-20">
          <h2 className="text-blue-600 text-xl mb-4">WHO <span className='text-black'>AM I?</span></h2>
          <p className="text-lg mb-6">
            Hello! I'm <PERSON><PERSON> — a Fullstack Developer passionate about building complete web and mobile applications. Since starting my coding journey in 2020, I've developed full-stack projects using modern frameworks, worked with databases, and built intuitive user interfaces.
          </p>
          <p className="text-lg mb-6">
            While my academic background is in science, my passion for problem-solving led me into software development, where I blend creativity with logic to build solutions. In addition to my work as a Fullstack Developer, I'm also currently studying data science, focusing on areas like data analysis, machine learning fundamentals, and Python-based data tools.
          </p>
          <p className="text-lg mb-8">
           I'm eager to contribute to meaningful projects, continue growing my web and mobile development expertise, and apply my developing data science skills as I learn.
          </p>
          <Link
            href="#contact"
            className="inline-flex items-center text-gray-600 hover:text-blue-600 font-medium transition-colors ml-0"
          >
            Contact Me
            <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  );
}
