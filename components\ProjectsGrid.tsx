"use client";
import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

// Extended projects array with more projects
const projects = [
  {
    id: 1,
    title: 'Mednex',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.',
    image: '/projects/home.PNG',
    tags: ['Vue', 'TailwindCSS', 'GSAP', 'Firebase', 'Figma'],
    liveUrl: 'https://mednex.example.com',
    githubUrl: 'https://github.com/username/mednex',
    category: 'web'
  },
  {
    id: 2,
    title: 'MyGitRepos',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.',
    image: '/projects/home.PNG',
    tags: ['React', 'CSS', 'GitHub API'],
    liveUrl: 'https://mygitrepos.example.com',
    githubUrl: 'https://github.com/username/mygitrepos',
    category: 'web'
  },
  {
    id: 3,
    title: 'Willi',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.',
    image: '/projects/home.PNG',
    tags: ['Vue', 'TailwindCSS', 'NodeJS', 'MongoDB', 'Figma'],
    liveUrl: 'https://willi.example.com',
    githubUrl: 'https://github.com/username/willi',
    category: 'app'
  },
  {
    id: 4,
    title: 'Customer Segmentation',
    description: 'A data science project that uses clustering algorithms to segment customers based on purchasing behavior, helping businesses target their marketing efforts more effectively.',
    image: '/projects/home.PNG',
    tags: ['Python', 'Scikit-learn', 'Pandas', 'Matplotlib'],
    liveUrl: 'https://github.com/username/customer-segmentation',
    githubUrl: 'https://github.com/username/customer-segmentation',
    category: 'datascience'
  },
  {
    id: 5,
    title: 'Weather App',
    description: 'A mobile application that provides real-time weather forecasts, alerts, and detailed meteorological data for locations worldwide.',
    image: '/projects/home.PNG',
    tags: ['React Native', 'Redux', 'Weather API'],
    liveUrl: 'https://play.google.com/store/apps/details?id=com.example.weatherapp',
    githubUrl: 'https://github.com/username/weather-app',
    category: 'app'
  },
  {
    id: 6,
    title: 'E-commerce Platform',
    description: 'A full-featured e-commerce platform with product catalog, shopping cart, user authentication, and payment processing integration.',
    image: '/projects/home.PNG',
    tags: ['Next.js', 'MongoDB', 'Stripe', 'TailwindCSS'],
    liveUrl: 'https://ecommerce-example.com',
    githubUrl: 'https://github.com/username/ecommerce-platform',
    category: 'web'
  },
  {
    id: 7,
    title: 'Task Management System',
    description: 'A collaborative task management application with real-time updates, task assignment, progress tracking, and deadline notifications.',
    image: '/projects/home.PNG',
    tags: ['React', 'Firebase', 'Material UI', 'Redux'],
    liveUrl: 'https://taskmanager-example.com',
    githubUrl: 'https://github.com/username/task-manager',
    category: 'web'
  },
  {
    id: 8,
    title: 'Sentiment Analysis Tool',
    description: 'A natural language processing tool that analyzes customer reviews and social media mentions to determine sentiment and extract key insights.',
    image: '/projects/home.PNG',
    tags: ['Python', 'NLTK', 'TensorFlow', 'Flask'],
    liveUrl: 'https://sentiment-analysis-tool.example.com',
    githubUrl: 'https://github.com/username/sentiment-analysis',
    category: 'datascience'
  },
  {
    id: 9,
    title: 'Fitness Tracker',
    description: 'A mobile application that tracks workouts, nutrition, and health metrics, providing personalized recommendations and progress visualization.',
    image: '/projects/home.PNG',
    tags: ['Flutter', 'Firebase', 'HealthKit', 'Google Fit API'],
    liveUrl: 'https://play.google.com/store/apps/details?id=com.example.fitnesstracker',
    githubUrl: 'https://github.com/username/fitness-tracker',
    category: 'app'
  },
  {
    id: 10,
    title: 'Stock Market Predictor',
    description: 'A machine learning model that predicts stock market trends based on historical data, news sentiment, and economic indicators.',
    image: '/projects/home.PNG',
    tags: ['Python', 'TensorFlow', 'Pandas', 'Plotly'],
    liveUrl: 'https://github.com/username/stock-predictor',
    githubUrl: 'https://github.com/username/stock-predictor',
    category: 'datascience'
  }
];

export default function ProjectsGrid() {
  const [activeTab, setActiveTab] = useState('all');
  
  const categories = [
    { id: 'all', label: 'All' },
    { id: 'web', label: 'Web' },
    { id: 'app', label: 'App' },
    { id: 'datascience', label: 'Data Science' }
  ];
  
  const filteredProjects = activeTab === 'all' 
    ? projects 
    : projects.filter(project => project.category === activeTab);

  return (
    <div className="container mx-auto px-4 md:px-8 relative z-10">
      <div className="max-w-6xl mx-auto">
        <h2 className="text-blue-400 text-xl mb-4">WHAT <span className="text-white">HAVE I BUILT?</span></h2>
        
        {/* Category Tabs - Minimal underline style */}
        <div className="flex justify-center mb-12 mt-8">
          <div className="inline-flex space-x-8">
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => setActiveTab(category.id)}
                className={`px-2 py-1 text-sm font-medium transition-all duration-300 relative ${
                  activeTab === category.id 
                    ? 'text-blue-400' 
                    : 'text-gray-400 hover:text-gray-200'
                }`}
              >
                {category.label}
                {activeTab === category.id && (
                  <span className="absolute bottom-0 left-0 w-full h-0.5 bg-blue-400 rounded-full"></span>
                )}
              </button>
            ))}
          </div>
        </div>

        <div className="space-y-32 mt-12">
          {filteredProjects.map((project, index) => (
            <div key={project.id} className="flex flex-col items-start">
              {/* Alternate layout for even/odd projects */}
              <div className={`relative w-full ${
                index % 2 === 0 
                  ? 'md:flex md:flex-row' 
                  : 'md:flex md:flex-row-reverse'
              }`}>
                {/* Project info */}
                <div className="md:w-1/2 flex flex-col z-10 md:px-8">
                  <span className="text-blue-400 text-sm uppercase mb-2 block">FEATURED PROJECT</span>
                  <h3 className="text-3xl font-bold mb-4 text-white">{project.title}</h3>
                  
                  {/* Description with dark background box */}
                  <div className="bg-slate-700/80 p-6 rounded-lg shadow-lg mb-6">
                    <p className="text-gray-300 leading-relaxed">{project.description}</p>
                  </div>
                  
                  <div className="flex flex-wrap gap-2 mb-6">
                    {project.tags.map((tag, tagIndex) => (
                      <span key={tagIndex} className="text-xs px-3 py-1 bg-slate-700 rounded text-gray-300">
                        {tag}
                      </span>
                    ))}
                  </div>
                  
                  {/* Project links */}
                  <div className="flex space-x-4">
                    <Link 
                      href={project.liveUrl}
                      target="_blank" 
                      className="text-xs text-gray-300 hover:text-blue-400 flex items-center"
                    >
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                      Live
                    </Link>
                    <Link 
                      href={project.githubUrl}
                      target="_blank" 
                      className="text-xs text-gray-300 hover:text-blue-400 flex items-center"
                    >
                      <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                      </svg>
                      GitHub
                    </Link>
                  </div>
                </div>
                
                {/* Project image - simple screenshot */}
                <div className="md:w-1/2 relative">
                  <div className="rounded-lg overflow-hidden shadow-xl">
                    <Image
                      src={project.image}
                      alt={project.title}
                      width={600}
                      height={400}
                      className="w-full h-auto object-cover"
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}




