 {/* Enhanced Hero Section */}
        <section className="py-20 md:py-28 bg-gradient-to-br from-white via-blue-50 to-indigo-50 overflow-hidden relative">
          {/* Decorative elements */}
          <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
            <div className="absolute top-20 left-10 w-64 h-64 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70"></div>
            <div className="absolute bottom-20 right-10 w-72 h-72 bg-indigo-100 rounded-full mix-blend-multiply filter blur-xl opacity-70"></div>
          </div>
          
          <div className="container mx-auto px-6 md:px-12 relative z-10">
            <div className="flex flex-col md:flex-row items-center gap-16 md:gap-20">
              <div className="md:w-1/2">
                <span className="inline-block px-4 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium mb-6 animate-fadeIn">
                  Front-end Developer
                </span>
                <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold mb-8 text-gray-900 leading-tight">
                  About <span className="text-blue-600 relative">
                    Me
                    <span className="absolute bottom-1 left-0 w-full h-3 bg-blue-100 -z-10 transform -rotate-1"></span>
                  </span>
                </h1>
                <div className="bg-white p-8 rounded-xl shadow-lg border border-gray-100 transform hover:scale-[1.02] transition-transform duration-300">
                  <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                    Hello! I'm <span className="font-semibold text-blue-700">Hasina Goosaar</span>, a passionate front-end developer focused on creating engaging and intuitive web experiences that captivate users.
                  </p>
                  <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                    With a background in science and a natural creative instinct, I bring a unique analytical and artistic perspective to my development work. Since 2022, I've been transforming concepts into responsive, accessible websites.
                  </p>
                  <p className="text-lg text-gray-700 leading-relaxed">
                    I thrive in collaborative environments where I can contribute my technical skills while continuing to grow as a developer.
                  </p>
                </div>
              </div>
              
              <div className="md:w-1/2">
                <div className="relative">
                  {/* Multiple decorative frames */}
                  <div className="absolute -inset-4 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-lg rotate-3 opacity-80"></div>
                  <div className="absolute -inset-4 bg-white rounded-lg -rotate-2 opacity-80"></div>
                  <div className="absolute -inset-1 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg rotate-1 opacity-20"></div>
                  
                  {/* Image with enhanced styling */}
                  <div className="relative overflow-hidden rounded-lg shadow-2xl border-4 border-white">
                    <Image 
                      src="/hasina.png" 
                      alt="Hasina Goosaar" 
                      width={500} 
                      height={600}
                      className="relative object-cover w-full h-auto max-h-[550px] transform hover:scale-105 transition-transform duration-700"
                      style={{
                        objectFit: 'cover',
                        objectPosition: 'center top'
                      }}
                      priority
                    />
                  </div>
                  
                  {/* Decorative elements */}
                  <div className="absolute -bottom-6 -right-6 w-24 h-24 bg-blue-50 rounded-full border-4 border-white shadow-lg flex items-center justify-center">
                    <span className="text-blue-600 font-bold text-lg">5+</span>
                    <span className="text-xs text-blue-600 ml-1">Years</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Skills Section - Keep as is */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-6 md:px-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center">
              My <span className="text-blue-600">Skills</span>
            </h2>
            
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 md:gap-8">
              {[
                { name: "React", level: 90 },
                { name: "Next.js", level: 85 },
                { name: "JavaScript", level: 92 },
                { name: "TypeScript", level: 80 },
                { name: "HTML/CSS", level: 95 },
                { name: "Tailwind CSS", level: 88 },
                { name: "UI/UX Design", level: 75 },
                { name: "Responsive Design", level: 90 }
              ].map((skill, index) => (
                <div key={index} className="bg-gray-50 p-5 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                  <h3 className="font-medium text-lg mb-3">{skill.name}</h3>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div 
                      className="bg-blue-600 h-2.5 rounded-full" 
                      style={{ width: `${skill.level}%` }}
                    ></div>
                  </div>
                  <p className="text-right mt-1 text-sm text-gray-500">{skill.level}%</p>
                </div>
              ))}
            </div>
          </div>
        </section>
        
        {/* Enhanced Services Section - This will be replaced by the ServicesSection component,
            but we're adding a custom wrapper with enhanced styling */}
        <section className="py-20 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
          {/* Decorative elements */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute top-0 left-0 w-full h-64 bg-gradient-to-b from-blue-50 to-transparent opacity-70"></div>
            <div className="absolute -top-24 -right-24 w-96 h-96 bg-indigo-50 rounded-full opacity-50"></div>
            <div className="absolute -bottom-24 -left-24 w-96 h-96 bg-blue-50 rounded-full opacity-50"></div>
          </div>
          
          <div className="container mx-auto px-6 md:px-12 relative z-10">
            <div className="text-center mb-16">
              <span className="inline-block px-4 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium mb-4">
                What I Offer
              </span>
              <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">
                My <span className="text-blue-600">Services</span>
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                I provide comprehensive solutions tailored to your specific needs, ensuring quality and excellence in every project.
              </p>
            </div>
          </div>
          
          <ServicesSection />
        </section>