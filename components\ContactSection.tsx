'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function ContactSection() {
  // Email address for contact
  const emailAddress = "<EMAIL>";
  const emailSubject = "Hello from your portfolio";
  const emailBody = "<PERSON> <PERSON><PERSON>, I'd like to connect with you!";
  const emailUrl = `mailto:${emailAddress}?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(emailBody)}`;

  return (
    <section id="contact" className="py-32 bg-gray-100 relative overflow-hidden">
      {/* Large "CONTACT" text in background - vertical */}
      <div className="absolute inset-0 pointer-events-none select-none overflow-hidden">
        <h2 className="text-[200px] md:text-[300px] lg:text-[400px] font-bold text-gray-200 opacity-30 tracking-widest absolute right-0 top-0 bottom-0 transform -rotate-90 origin-right translate-y-1/4">
          CONTACT
        </h2>
      </div>

      <div className="container mx-auto px-4 md:px-8 relative z-10">
        <div className="text-center mb-16 max-w-4xl mx-auto">
          <h2 className="text-blue-600 text-2xl mb-4">WHAT<span className="text-gray-800">&apos;S NEXT?</span></h2>
          <h3 className="text-5xl md:text-6xl font-bold mb-8 text-gray-800">GET IN TOUCH</h3>
          <p className="text-gray-600 text-xl max-w-3xl mx-auto mb-12">
            I&apos;m currently open to new opportunities, and my inbox is always open. Whether
            you&apos;re interested in hiring me, have a question, or just want to say hi, I&apos;d love to
            hear from you and will definitely get back to you.
          </p>
          
          <div className="mt-12">
            <Link
              href={emailUrl}
              className="inline-flex items-center px-10 py-4 border-2 border-gray-400 text-gray-600 text-xl rounded-md font-medium hover:border-gray-600 hover:text-gray-800 transition-colors"
            >
              <svg 
                className="w-6 h-6 mr-2" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24" 
                xmlns="http://www.w3.org/2000/svg"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              Say Hello
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}



