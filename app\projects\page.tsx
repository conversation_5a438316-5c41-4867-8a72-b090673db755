import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ProjectsGrid from '@/components/ProjectsGrid';
import Link from 'next/link';
import SocialLinks from '@/components/SocialLinks';
import ScrollIndicator from '@/components/ScrollIndicator';

export default function ProjectsPage() {
  return (
    <>
      <Header />
      <SocialLinks />
      <ScrollIndicator />
      
      <main className="pt-24">
        {/* Hero Section */}
        <section className="py-16  relative overflow-hidden">
          {/* Large "PROJECTS" text in background */}
          <div className="absolute inset-0 pointer-events-none select-none overflow-hidden">
            <h2 className="text-[160px] md:text-[250px] lg:text-[350px] font-bold text-gray-100 opacity-30 tracking-widest absolute -right-20 top-1/2 transform -translate-y-1/2">
              PROJECTS
            </h2>
          </div>
          
          <div className="container mx-auto px-6 md:px-12 relative z-10">
            <div className="max-w-4xl mx-auto text-left mb-12 md:ml-0">
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                My <span className="text-blue-600">Portfolio</span>
              </h1>
              <p className="text-lg text-gray-600">
                Here's a comprehensive collection of projects I've worked on. Each project represents a unique challenge and 
                showcases different skills and technologies.
              </p>
            </div>
          </div>
        </section>

        {/* Projects Grid Section */}
        <section className="py-16 bg-slate-800 text-white relative overflow-hidden">
          <div className="container mx-auto px-6 md:px-12">
            <div className="max-w-6xl mx-auto md:ml-0">
              <ProjectsGrid />
            </div>
          </div>
        </section>
        
        {/* Call to Action */}
      
      </main>
      
      <Footer />
    </>
  );
}




