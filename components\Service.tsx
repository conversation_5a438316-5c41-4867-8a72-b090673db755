import React from 'react';

// ✅ Enhanced props type for the ServiceItem component
type ServiceItemProps = {
  icon: React.ReactNode;
  title: string;
  shortDescription: string;
  bgColor?: string;
  hoverColor?: string;
};

// ✅ Enhanced ServiceItem component with hover effects and better styling
const ServiceItem = ({ 
  icon, 
  title, 
  shortDescription, 
  bgColor = "bg-white", 
  hoverColor = "hover:border-blue-500" 
}: ServiceItemProps) => {
  return (
    <div className={`p-8 rounded-xl shadow-lg ${bgColor} text-left flex flex-col h-full border-b-4 border-transparent ${hoverColor} transition-all duration-300 hover:-translate-y-2 group`}>
      <div className="mb-6 text-blue-600 flex-shrink-0 transform transition-transform duration-300 group-hover:scale-110">
        {icon}
      </div>
      <h3 className="text-2xl font-bold text-gray-800 mb-4 group-hover:text-blue-600 transition-colors">
        {title}
      </h3>
      <p className="text-gray-600 flex-grow leading-relaxed">
        {shortDescription}
      </p>
      <div className="mt-6 pt-4 border-t border-gray-100">
        <span className="inline-flex items-center text-sm font-medium text-blue-600 group-hover:text-blue-700">
          Learn more
          <svg className="ml-1 w-4 h-4 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </span>
      </div>
    </div>
  );
};

const ServicesSection = () => {
  return (
    <section className="py-16 md:py-24 bg-gradient-to-b from-gray-50 to-white text-gray-900 overflow-hidden relative">
      {/* Decorative background elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute right-0 top-0 w-1/3 h-1/3 bg-blue-50 opacity-50 rounded-bl-full"></div>
        <div className="absolute left-0 bottom-0 w-1/4 h-1/4 bg-indigo-50 opacity-50 rounded-tr-full"></div>
      </div>
      
      <div className="container mx-auto px-6 md:px-12 relative z-10">
        <div className="text-center mb-16">
          <span className="inline-block px-4 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium mb-4">
            What I Offer
          </span>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900">
            My <span className="text-blue-600">Services</span>
          </h2>
          <p className="text-gray-600 text-lg mb-8 max-w-2xl mx-auto leading-relaxed">
            I provide comprehensive solutions tailored to your specific needs, ensuring quality and excellence in every project.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-10 items-stretch">
          <ServiceItem
            icon={
              <svg xmlns="http://www.w3.org/2000/svg" className="h-14 w-14" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                <path strokeLinecap="round" strokeLinejoin="round" d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            }
            title="Mobile Apps"
            shortDescription="I develop intuitive, high-performance mobile applications that provide seamless user experiences across all devices. From concept to deployment, I ensure your app stands out in the market."
            hoverColor="hover:border-blue-500"
          />

          <ServiceItem
            icon={
              <svg xmlns="http://www.w3.org/2000/svg" className="h-14 w-14" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            }
            title="Websites"
            shortDescription="I create responsive, modern websites that engage visitors and drive conversions. Using the latest technologies, I build fast-loading, SEO-friendly sites that help your business grow online."
            hoverColor="hover:border-indigo-500"
          />

          <ServiceItem
            icon={
              <svg xmlns="http://www.w3.org/2000/svg" className="h-14 w-14" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
                <path strokeLinecap="round" strokeLinejoin="round" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
              </svg>
            }
            title="Backend Systems & APIs"
            shortDescription="I design and implement robust backend systems and APIs that power your applications. With a focus on security, scalability, and performance, I build the foundation your digital products need."
            hoverColor="hover:border-purple-500"
          />
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;

